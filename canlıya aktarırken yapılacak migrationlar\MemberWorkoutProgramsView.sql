-- Üye Program Atama Sistemi View Script
-- Bu script performans için optimize edilmiş view oluşturur

USE [GymProject]
GO

-- CACHE OPTİMİZASYONU İÇİN VİEW (Sık kullanılan join'ler için)
-- Schema bound view - indexlenebilir performans için
CREATE VIEW [dbo].[vw_MemberWorkoutProgramDetails]
WITH SCHEMABINDING
AS
SELECT
    mwp.MemberWorkoutProgramID,
    mwp.MemberID,
    m.Name AS MemberName,
    m.PhoneNumber AS MemberPhone,
    mwp.WorkoutProgramTemplateID,
    wpt.ProgramName,
    wpt.Description AS ProgramDescription,
    wpt.ExperienceLevel,
    wpt.TargetGoal,
    mwp.CompanyID,
    mwp.AssignedDate,
    mwp.StartDate,
    mwp.EndDate,
    mwp.Notes,
    mwp.IsActive,
    mwp.CreationDate
FROM dbo.MemberWorkoutPrograms mwp
INNER JOIN dbo.Members m ON mwp.MemberID = m.MemberID
INNER JOIN dbo.WorkoutProgramTemplates wpt ON mwp.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID
WHERE mwp.IsActive = 1 AND m.IsActive = 1 AND wpt.IsActive = 1
GO

-- View için index (performans için)
CREATE UNIQUE CLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_Clustered]
ON [dbo].[vw_MemberWorkoutProgramDetails] ([MemberWorkoutProgramID])
GO

-- Ek performans view'ı - Program istatistikleri için (schema bound olmayan)
CREATE VIEW [dbo].[vw_MemberWorkoutProgramStats] AS
SELECT
    mwp.MemberWorkoutProgramID,
    mwp.MemberID,
    mwp.WorkoutProgramTemplateID,
    mwp.CompanyID,
    -- Program gün sayısı
    (SELECT COUNT(*) FROM WorkoutProgramDays wpd WHERE wpd.WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID) AS DayCount,
    -- Program egzersiz sayısı
    (SELECT COUNT(*) FROM WorkoutProgramExercises wpe
     INNER JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
     WHERE wpd.WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID) AS ExerciseCount,
    -- Aktif program sayısı (üye bazında)
    (SELECT COUNT(*) FROM MemberWorkoutPrograms mwp2
     WHERE mwp2.MemberID = mwp.MemberID AND mwp2.IsActive = 1) AS ActiveProgramCount
FROM MemberWorkoutPrograms mwp
WHERE mwp.IsActive = 1
GO

PRINT 'Üye Program Atama View''ları oluşturuldu!'
PRINT '- vw_MemberWorkoutProgramDetails (Schema bound - indexli detaylı view)'
PRINT '- vw_MemberWorkoutProgramStats (İstatistik view)'
PRINT 'View indexi eklendi.'
GO
